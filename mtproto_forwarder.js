const { TelegramClient } = require('telegram');
const { StringSession } = require('telegram/sessions');
const { NewMessage } = require('telegram/events');
const schedule = require('node-schedule');

// 请替换这些配置为您自己的数据
const apiId = 15619267; // 使用图片中的 API ID
const apiHash = 'c3f29b5d2bd7975bfab99b6789e994a6'; // 使用图片中的 API Hash
const stringSession = new StringSession();  // 已保存的会话字符串

// 存储定时任务和用户状态
const scheduledJobs = {};
const userStates = {};
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 10;
let client = null;

// 网络连接配置
const CONNECTION_CONFIG = {
    currentMode: null, // 'direct' 或 'proxy'
    directTested: false,
    proxyTested: false,
    directWorks: false,
    proxyWorks: false
};

// 代理配置
const PROXY_CONFIG = {
    socksType: 5,
    ip: '127.0.0.1',
    port: 10020,
    // username: '',
    // password: '',
};

// 添加一个全局对象用于临时存储可能属于同一相册的消息
const pendingAlbums = {};

// 添加帖子组管理相关常量和变量
const POST_GROUP_TIMEOUT = 5000; // 5秒内的连续转发消息被视为同一帖子组
const pendingPostGroups = {}; // 存储正在收集的帖子组

// 为pendingAlbums添加过期清理功能
setInterval(() => {
    const now = Date.now();
    for (const userId in pendingAlbums) {
        if (pendingAlbums[userId].lastUpdate < now - 60000) { // 1分钟无活动则清理
            delete pendingAlbums[userId];
            console.log(`[DEBUG] 清理闲置用户 ${userId} 的临时相册数据`);
        }
    }
}, 30000); // 每30秒检查一次

// 命令列表
const COMMANDS = {
  START: '/start',
  HELP: '/help',
  SCHEDULE: '/schedule',
  LIST: '/list',
  CANCEL: '/cancel',
  STATUS: '/status'
};

// 用户交互状态
const STATES = {
  IDLE: 'idle',
  AWAITING_TARGETS: 'awaiting_targets', // 等待选择转发对象
  COLLECTING_TARGETS: 'collecting_targets', // 正在收集转发对象
  AWAITING_MESSAGES: 'awaiting_messages', // 等待输入转发内容
  COLLECTING_MESSAGES: 'collecting_messages', // 正在收集转发内容
  COLLECTING_POST: 'collecting_post', // 正在收集转发帖子
  AWAITING_TIME: 'awaiting_time', // 等待设置时间
  AWAITING_CONFIRMATION: 'awaiting_confirmation' // 等待确认
};

// 系统命令
const SYSTEM_COMMANDS = {
  DONE: '/done', // 完成当前收集阶段
  CANCEL: '/cancel', // 取消操作
  CLEAR: '/clear' // 清除已收集数据
};

// 帮助信息
const HELP_TEXT = `
📅 定时转发机器人使用指南：

命令列表：
/start - 开始使用机器人
/help - 查看帮助信息
/schedule - 创建新的定时任务
/list - 查看当前的定时任务
/cancel <任务ID> - 取消一个定时任务
/status - 查看连接状态和系统信息

创建定时任务的步骤：
1. 发送 /schedule 命令
2. 添加转发目标（可添加多个），输入 /done 结束添加
3. 添加要发送的消息（可添加多条），输入 /done 结束添加
   - 支持所有格式：文字、图片、视频、动态表情等均保持原格式
4. 设置发送时间（格式: YYYY-MM-DD HH:MM）
5. 确认创建任务

其他命令：
/done - 完成当前输入阶段
/clear - 清除当前已输入的数据
/cancel - 取消当前操作

🌐 网络连接：
系统支持智能网络连接，会自动选择最佳连接方式：
- 优先使用直连（更快更稳定）
- 直连失败时自动切换到代理连接
- 支持断线重连和连接方式自动切换
`;

// 添加管理员配置
const ADMIN_USERS = [
    // 填写管理员的Telegram用户ID，例如:
    // 123456789,
    // 987654321
    8168427064,
    7185781118,
    6410856821
];

// 添加用户权限检查函数
async function checkUserPermission(userId) {
    // 如果ADMIN_USERS为空，允许所有用户
    if (ADMIN_USERS.length === 0) {
        return true;
    }
    
    // 如果用户在管理员列表中，则允许访问
    return ADMIN_USERS.includes(Number(userId));
}

// 获取连接状态信息
async function getConnectionStatus() {
    const currentTime = new Date();
    const uptime = process.uptime();
    const uptimeStr = `${Math.floor(uptime / 3600)}小时${Math.floor((uptime % 3600) / 60)}分钟`;
    
    let statusMsg = `🤖 机器人状态报告\n\n`;
    
    // 连接状态
    statusMsg += `🌐 网络连接状态:\n`;
    statusMsg += `├─ 当前模式: ${CONNECTION_CONFIG.currentMode === 'direct' ? '🌐 直连' : CONNECTION_CONFIG.currentMode === 'proxy' ? '🔒 代理' : '❓ 未知'}\n`;
    
    if (CONNECTION_CONFIG.currentMode === 'proxy') {
        statusMsg += `├─ 代理服务器: ${PROXY_CONFIG.ip}:${PROXY_CONFIG.port}\n`;
        statusMsg += `├─ 代理类型: SOCKS${PROXY_CONFIG.socksType}\n`;
    }
    
    statusMsg += `├─ 直连状态: ${CONNECTION_CONFIG.directTested ? (CONNECTION_CONFIG.directWorks ? '✅ 可用' : '❌ 不可用') : '❓ 未测试'}\n`;
    statusMsg += `└─ 代理状态: ${CONNECTION_CONFIG.proxyTested ? (CONNECTION_CONFIG.proxyWorks ? '✅ 可用' : '❌ 不可用') : '❓ 未测试'}\n`;
    
    // 系统信息
    statusMsg += `\n📊 系统信息:\n`;
    statusMsg += `├─ 运行时间: ${uptimeStr}\n`;
    statusMsg += `├─ 重连次数: ${reconnectAttempts}\n`;
    statusMsg += `├─ 当前时间: ${formatDate(currentTime)}\n`;
    
    // 任务统计
    const totalJobs = Object.keys(scheduledJobs).length;
    statusMsg += `└─ 定时任务数: ${totalJobs}个\n`;
    
    // 客户端状态
    statusMsg += `\n📱 客户端状态:\n`;
    try {
        const me = await client.getMe();
        statusMsg += `├─ 登录账户: ${me.firstName || ''}${me.lastName ? ' ' + me.lastName : ''}\n`;
        statusMsg += `├─ 用户名: ${me.username ? '@' + me.username : '未设置'}\n`;
        statusMsg += `├─ 用户ID: ${me.id}\n`;
        statusMsg += `└─ 连接状态: ✅ 已连接\n`;
    } catch (error) {
        statusMsg += `└─ 连接状态: ❌ 连接异常 (${error.message})\n`;
    }
    
    return statusMsg;
}

async function main() {
    await initClient();
}

// 创建客户端配置
function createClientConfig(useProxy = false) {
    const baseConfig = {
        connectionRetries: 3, // 降低单次尝试的重试次数，因为我们有多种连接方式
        maxConcurrentDownloads: 5,
        retryDelay: 2000, // 2秒重试间隔
        autoReconnect: true,
        requestRetries: 3,
        timeout: 15000, // 15秒超时，更快检测连接失败
        useWSS: false,
    };

    if (useProxy) {
        baseConfig.proxy = PROXY_CONFIG;
    }

    return baseConfig;
}

// 尝试连接并启动客户端
async function attemptConnection(useProxy = false) {
    const connectionType = useProxy ? '代理' : '直连';
    console.log(`🔍 尝试${connectionType}启动...`);
    
    try {
        // 创建启动配置
        const config = createClientConfig(useProxy);
        config.timeout = 8000; // 8秒超时
        config.connectionRetries = 2; // 最多2次重试
        config.requestRetries = 2;
        
        const attemptClient = new TelegramClient(stringSession, apiId, apiHash, config);
        
        console.log(`   ├─ 正在连接Telegram服务器${useProxy ? '（通过代理）' : ''}...`);
        
        // 设置启动参数
        await attemptClient.start({
            phoneNumber: async () => { 
                throw new Error('不应该需要输入手机号（会话已保存）'); 
            },
            password: async () => { 
                throw new Error('不应该需要输入密码（会话已保存）'); 
            },
            phoneCode: async () => { 
                throw new Error('不应该需要输入验证码（会话已保存）'); 
            },
            onError: (err) => {
                console.log(`${connectionType}启动失败:`, err.message);
                return false;
            },
        });
        
        console.log(`   ├─ 连接成功，正在验证账户...`);
        
        // 验证登录
        const me = await attemptClient.getMe();
        console.log(`   └─ ✅ ${connectionType}启动成功！账户: ${me.firstName || 'Unknown'}`);
        
        // 返回成功的客户端
        return attemptClient;
        
    } catch (error) {
        const errorMsg = error.message.includes('ETIMEDOUT') ? 'ETIMEDOUT (连接超时)' : 
                        error.message.includes('TIMEOUT') ? 'TIMEOUT (超时)' :
                        error.message.includes('Not connected') ? 'NOT_CONNECTED (连接断开)' :
                        error.message;
        
        console.log(`   └─ ❌ ${connectionType}启动失败: ${errorMsg}`);
        return null;
    }
}

// 智能初始化客户端
async function initClient() {
    try {
        console.log('🚀 正在初始化智能连接客户端...\n');
        
        // 方法：直接尝试启动，失败时切换
        console.log('📡 开始智能连接启动...\n');
        
        // 智能选择启动顺序
        let firstAttempt, secondAttempt, firstType, secondType;
        
        // 根据之前的成功记录决定尝试顺序
        if (CONNECTION_CONFIG.proxyWorks === true && CONNECTION_CONFIG.directWorks === false) {
            // 之前代理成功过，直连失败过，优先代理
            firstAttempt = true; firstType = '代理';
            secondAttempt = false; secondType = '直连';
        } else {
            // 默认或之前直连成功过，优先直连
            firstAttempt = false; firstType = '直连';
            secondAttempt = true; secondType = '代理';
        }
        
        // 第一次尝试
        console.log(`1️⃣ 第一次尝试：${firstType}启动`);
        client = await attemptConnection(firstAttempt);
        
        if (client) {
            // 第一次尝试成功
            CONNECTION_CONFIG.currentMode = firstAttempt ? 'proxy' : 'direct';
            if (firstAttempt) {
                CONNECTION_CONFIG.proxyWorks = true;
                CONNECTION_CONFIG.proxyTested = true;
            } else {
                CONNECTION_CONFIG.directWorks = true;
                CONNECTION_CONFIG.directTested = true;
            }
            console.log(`\n✅ ${firstType}启动成功！`);
        } else {
            // 第一次失败，尝试第二种方式
            console.log(`\n2️⃣ 第二次尝试：${secondType}启动`);
            client = await attemptConnection(secondAttempt);
            
            if (client) {
                // 第二次尝试成功
                CONNECTION_CONFIG.currentMode = secondAttempt ? 'proxy' : 'direct';
                if (secondAttempt) {
                    CONNECTION_CONFIG.proxyWorks = true;
                    CONNECTION_CONFIG.proxyTested = true;
                    CONNECTION_CONFIG.directWorks = false;
                    CONNECTION_CONFIG.directTested = true;
                } else {
                    CONNECTION_CONFIG.directWorks = true;
                    CONNECTION_CONFIG.directTested = true;
                    CONNECTION_CONFIG.proxyWorks = false;
                    CONNECTION_CONFIG.proxyTested = true;
                }
                console.log(`\n✅ ${secondType}启动成功！`);
            } else {
                // 两种方式都失败
                console.log('\n❌ 所有连接方式都失败！');
                throw new Error('直连和代理启动都失败，请检查网络配置和代理服务器状态');
            }
        }
        
        // 显示最终选择的连接方式
        console.log('\n📊 连接结果:');
        if (CONNECTION_CONFIG.currentMode === 'direct') {
            console.log('🎯 成功使用：🌐 直连模式');
        } else {
            console.log(`🎯 成功使用：🔒 代理模式 (${PROXY_CONFIG.ip}:${PROXY_CONFIG.port})`);
        }
        
        console.log('\n📱 客户端已就绪...');

        // 设置断开连接时的处理函数
        client.session.setDC = (dcId, host, port) => {
            console.log(`更改数据中心连接: DC${dcId} (${host}:${port})`);
            client.session._dcId = dcId;
            client.session._serverAddress = host;
            client.session._port = port;
        };

        // 客户端已经在attemptConnection中启动完成
        // 获取账号信息进行最终验证
        const me = await client.getMe();
        console.log('   ├─ 最终验证通过');
        console.log('   └─ ✅ 系统就绪!');
        console.log(`      账号: ${me.firstName || ''}${me.lastName ? ' ' + me.lastName : ''}`);
        console.log(`      用户名: ${me.username ? '@' + me.username : '未设置'}`);
        console.log(`      ID: ${me.id.toString()}`);

        // 保存会话字符串（已保存到代码中）
        // console.log('会话字符串:', client.session.save()); // 调试时取消注释

        console.log('\n🎧 开始监听消息...');
        
        // 设置连接状态监听
        client.addEventHandler((update) => {
            if (update.className === 'UpdateConnectionState') {
                if (update.state.className === 'ConnectionStateConnecting') {
                    console.log('正在连接Telegram服务器...');
                } else if (update.state.className === 'ConnectionStateConnected') {
                    console.log('已连接到Telegram服务器');
                    reconnectAttempts = 0; // 重置重连计数
                }
            }
        });

        // 监听所有新消息事件
        client.addEventHandler(async (event) => {
            try {
                // 检查消息是否存在
                if (!event.message) {
                    return;
                }
                
                // 记录收到的每一条消息的详细信息
                const msgId = event.message.id || '未知ID';
                console.log(`[DEBUG] 收到新消息: ID=${msgId}, 文本=${event.message.message ? `"${event.message.message.substring(0, 30)}${event.message.message.length > 30 ? '...' : ''}"` : '(无文本)'}`);
                
                // 调试信息：记录消息的完整结构
                if (event.message.media) {
                    console.log(`[DEBUG] 收到媒体消息，类型: ${event.message.media.className}`);
                    if (event.message.groupedId) {
                        console.log(`[DEBUG] 检测到相册消息，groupedId: ${event.message.groupedId}`);
                    }
                    
                    // 输出更多媒体信息
                    try {
                        if (event.message.media.photo) {
                            console.log(`[DEBUG] 照片详情: ID=${event.message.media.photo.id}, 大小=${JSON.stringify(event.message.media.photo.sizes)}`);
                        } else if (event.message.media.document) {
                            console.log(`[DEBUG] 文档详情: ID=${event.message.media.document.id}, MIME=${event.message.media.document.mimeType}`);
                            // 输出文档属性
                            if (event.message.media.document.attributes) {
                                console.log(`[DEBUG] 文档属性: ${JSON.stringify(event.message.media.document.attributes)}`);
                            }
                        }
                    } catch (err) {
                        console.log(`[DEBUG] 获取媒体详情出错: ${err.message}`);
                    }
                }
                
                // 获取发送者和聊天信息
                let sender, chat;
                try {
                    sender = await event.message.getSender();
                    chat = await event.message.getChat();
                } catch (error) {
                    console.error('获取消息信息失败:', error.message);
                    return;
                }
                
                if (!sender) {
                    console.error('无法获取发送者信息');
                    return;
                }
                
                console.log(`[DEBUG] 发送者: ID=${sender.id}, 是否机器人=${sender.bot || false}, 名称=${sender.firstName || ''} ${sender.lastName || ''}`);
                console.log(`[DEBUG] 聊天: ID=${chat.id}, 类型=${chat.isGroup ? '群组' : (chat.isChannel ? '频道' : '私聊')}`);
                
                // 检查是否自己发给自己的消息(Saved Messages)
                const me = await client.getMe();
                const isSavedMessages = chat.id.toString() === me.id.toString();
                
                // 处理消息类型 - 修复聊天类型判断，确保能处理私聊
                const isPrivateChat = chat.className === 'User';
                const isGroupChat = chat.isGroup || chat.className === 'Chat' || chat.className === 'ChatFull';
                
                console.log(`[DEBUG] 聊天类型详细信息: className=${chat.className}, isPrivate=${isPrivateChat}, isGroup=${isGroupChat}`);
                
                if (!isPrivateChat && !isSavedMessages && !isGroupChat) {
                    console.log(`[DEBUG] 跳过非私聊/群聊/Saved Messages的消息，聊天类型: ${chat.className}`);
                    return;
                }
                
                // 获取消息内容和用户ID
                const message = event.message.message || '';
                const userId = isSavedMessages ? me.id.toString() : sender.id.toString();
                
                console.log(`[DEBUG] 处理消息: 用户=${userId}, 内容="${message.substring(0, 30)}${message.length > 30 ? '...' : ''}", 将处理此消息`);
                
                // 初始化用户状态（如果不存在）
                if (!userStates[userId]) {
                    userStates[userId] = {
                        state: STATES.IDLE,
                        data: {
                            targets: [],
                            messages: [],
                            messageIds: [],
                            chatIds: [],
                            groupedIds: [],
                            postIds: [],
                            postUnits: []
                        }
                    };
                }

                // 处理命令或基于当前状态的输入，传递完整event对象
                await handleUserInput(client, userId, message, event);
                
            } catch (err) {
                console.error('处理消息时出错:', err);
                
                        // 如果是连接错误，尝试重连
        if ((err.message && (err.message.includes('Not connected') || 
                            err.message.includes('ETIMEDOUT') || 
                            err.message.includes('TIMEOUT'))) && 
            reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
            console.log('🔄 检测到连接错误，开始智能重连...');
            await handleReconnect();
        }
            }
        }, new NewMessage({}));

        // 定期检查连接状态并尝试重连
        setInterval(async () => {
            try {
                // 尝试简单的API调用来检查连接
                if (client) {
                    await client.getMe();
                }
            } catch (error) {
                console.error('🔍 连接检查失败:', error.message);
                if ((error.message.includes('Not connected') || 
                     error.message.includes('ETIMEDOUT') || 
                     error.message.includes('TIMEOUT')) && 
                    reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                    console.log('🔄 定期检查发现连接问题，开始智能重连...');
                    await handleReconnect();
                }
            }
        }, 60000); // 每分钟检查一次

        // 显示启动摘要
        console.log('\n' + '='.repeat(50));
        console.log('🎉 定时转发机器人启动成功！');
        console.log('='.repeat(50));
        console.log(`🌐 连接方式: ${CONNECTION_CONFIG.currentMode === 'direct' ? '🌐 直连模式' : '🔒 代理模式'}`);
        if (CONNECTION_CONFIG.currentMode === 'proxy') {
            console.log(`🔗 代理服务器: ${PROXY_CONFIG.ip}:${PROXY_CONFIG.port}`);
        }
        console.log(`👥 授权管理员: ${ADMIN_USERS.length}人`);
        console.log(`📊 当前任务数: 0个`);
        console.log('─'.repeat(50));
        console.log('📝 主要命令:');
        console.log('  /help     - 查看完整帮助');
        console.log('  /status   - 查看系统状态');
        console.log('  /schedule - 创建定时任务');
        console.log('─'.repeat(50));
        console.log('🛑 按 Ctrl+C 安全退出\n');
    } catch (err) {
        console.error('客户端初始化失败:', err);
        
        if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
            await handleReconnect();
        } else {
            console.error(`已达到最大重连次数 (${MAX_RECONNECT_ATTEMPTS})，程序退出`);
            process.exit(1);
        }
    }
}

// 重置连接状态，强制重新测试
function resetConnectionState() {
    console.log('🔄 重置连接状态...');
    CONNECTION_CONFIG.directTested = false;
    CONNECTION_CONFIG.proxyTested = false;
    CONNECTION_CONFIG.directWorks = false;
    CONNECTION_CONFIG.proxyWorks = false;
    CONNECTION_CONFIG.currentMode = null;
}

// 智能重连处理
async function handleReconnect() {
    reconnectAttempts++;
    const delay = Math.min(reconnectAttempts * 2000, 10000); // 递增延迟，最大10秒
    
    console.log(`🔄 连接失败，第 ${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS} 次尝试重连，等待 ${delay/1000} 秒...`);
    
    // 等待一段时间再重连
    await new Promise(resolve => setTimeout(resolve, delay));
    
    // 如果客户端存在，尝试断开连接
    if (client) {
        try {
            await client.disconnect();
        } catch (e) {
            console.log('断开旧连接时出错:', e.message);
        }
    }
    
    // 重连时的智能切换策略
    if (reconnectAttempts === 1) {
        // 第一次重连立即尝试切换连接方式
        console.log('🔄 立即尝试切换连接方式...');
        if (CONNECTION_CONFIG.currentMode === 'direct') {
            console.log('🔒 从直连切换到代理启动...');
            // 标记直连不可用，下次重新初始化时会使用代理
            CONNECTION_CONFIG.directWorks = false;
            CONNECTION_CONFIG.proxyWorks = true; // 假设代理可用，让initClient尝试
        } else if (CONNECTION_CONFIG.currentMode === 'proxy') {
            console.log('🌐 从代理切换到直连启动...');
            // 标记代理不可用，下次重新初始化时会使用直连
            CONNECTION_CONFIG.proxyWorks = false;
            CONNECTION_CONFIG.directWorks = true; // 假设直连可用，让initClient尝试
        } else {
            // 如果当前模式未知，重置并重新测试
            resetConnectionState();
        }
    } else if (reconnectAttempts % 2 === 0) {
        // 每2次重连尝试，重置状态让系统重新选择最佳连接
        console.log('🔍 重连多次失败，重置连接状态...');
        resetConnectionState();
    }
    
    // 重新初始化客户端
    await initClient();
}

// 处理用户输入
async function handleUserInput(client, userId, message, event) {
    console.log(`[DEBUG] 处理用户 ${userId} 的输入: ${message.substring(0, 30)}${message.length > 30 ? '...' : ''}`);
    
    // 检查用户权限
    const hasPermission = await checkUserPermission(userId);
    if (!hasPermission) {
        // 用户无权限，发送提示
        await sendMessage(client, userId, "⛔ 您没有使用此机器人的权限。请联系管理员添加您的用户ID。");
        console.log(`[WARN] 用户 ${userId} 尝试访问但没有权限`);
        return;
    }

    // 确保用户状态已初始化
    if (!userStates[userId]) {
        userStates[userId] = {
            state: STATES.IDLE,
            data: {
                targets: [],
                messages: [],
                messageIds: [],
                chatIds: [],
                groupedIds: [],
                postIds: [],
                postUnits: []
            }
        };
    }
    
    const userState = userStates[userId];
    console.log(`[DEBUG] 处理用户输入 - 用户: ${userId}, 状态: ${userState.state}, 消息: ${message.substring(0, 30)}${message.length > 30 ? '...' : ''}`);
    
    // 确保data对象存在并初始化
    if (!userState.data) {
        userState.data = {
            targets: [],
            messages: [],
            messageIds: [],
            chatIds: [],
            groupedIds: [],
            postIds: [],
            postUnits: []
        };
    }
    
    // 处理命令（优先级高于状态）
    if (message.startsWith('/')) {
        console.log(`[DEBUG] 处理命令: ${message}, 当前状态: ${userState.state}`);
        
        // 处理系统命令 /done、/clear
        if (message === SYSTEM_COMMANDS.DONE) {
            console.log(`[DEBUG] 执行 /done 命令处理函数`);
            return await handleDoneCommand(client, userId);
        } else if (message === SYSTEM_COMMANDS.CLEAR) {
            return await handleClearCommand(client, userId);
        }
        
        // 处理其他命令 - 除了/cancel和/list外的命令会重置状态
        if (message !== COMMANDS.CANCEL && message !== COMMANDS.LIST) {
            console.log(`[DEBUG] 重置状态，原状态: ${userState.state}`);
            userState.state = STATES.IDLE;
            userState.data = {
                targets: [],
                messages: [],
                messageIds: [],
                chatIds: [],
                groupedIds: [],
                postIds: [],
                postUnits: []
            };
        }
        
        // 处理命令
        if (message === COMMANDS.START) {
            await sendMessage(client, userId, `👋 欢迎使用定时转发机器人！\n\n发送 /help 查看使用指南。`);
            
        } else if (message === COMMANDS.HELP) {
            await sendMessage(client, userId, HELP_TEXT);
            
        } else if (message === COMMANDS.SCHEDULE) {
            // 初始化数据结构
            userState.state = STATES.AWAITING_TARGETS;
            userState.data = {
                targets: [],
                messages: [],
                messageIds: [],
                chatIds: [],
                groupedIds: [],
                postIds: [],
                postUnits: [],
                scheduledTime: null
            };
            console.log(`[DEBUG] 设置状态为 ${userState.state}，初始化数据结构`);
            
            await sendMessage(client, userId, 
                `👥 请输入转发目标对象，可以是:\n\n` +
                `- 用户名格式: @username\n` +
                `- 聊天ID格式: -1001234567890\n\n` +
                `📝 每次输入一个目标，可以添加多个目标\n` +
                `输入 ${SYSTEM_COMMANDS.DONE} 完成添加目标\n` +
                `输入 ${SYSTEM_COMMANDS.CANCEL} 取消操作`);
                
        } else if (message === COMMANDS.LIST) {
            await listJobs(client, userId);
            
        } else if (message === COMMANDS.STATUS) {
            const statusInfo = await getConnectionStatus();
            await sendMessage(client, userId, statusInfo);
            
        } else if (message.startsWith(COMMANDS.CANCEL)) {
            // 格式: /cancel 任务ID
            const parts = message.split(' ');
            if (parts.length > 1) {
                const jobId = parts[1];
                await cancelJob(client, userId, jobId);
            } else {
                // 取消当前操作
                userState.state = STATES.IDLE;
                userState.data = {
                    targets: [],
                    messages: [],
                    messageIds: [],
                    chatIds: [],
                    groupedIds: [],
                    postIds: [],
                    postUnits: []
                };
                await sendMessage(client, userId, "❌ 已取消当前操作");
            }
            
        } else {
            await sendMessage(client, userId, "❓ 未知命令。发送 /help 查看可用命令。");
        }
        
        return;
    }
    
    // 基于状态处理用户输入
    switch (userState.state) {
        case STATES.AWAITING_TARGETS:
        case STATES.COLLECTING_TARGETS:
            // 收集转发目标
            userState.data.targets.push(message);
            userState.state = STATES.COLLECTING_TARGETS;
            console.log(`[DEBUG] 添加目标: ${message}, 当前状态: ${userState.state}, 目标总数: ${userState.data.targets.length}`);
            
            await sendMessage(client, userId, 
                `✅ 已添加目标: ${message}\n` +
                `当前已添加 ${userState.data.targets.length} 个目标\n\n` +
                `继续输入下一个目标，或:\n` +
                `- 输入 ${SYSTEM_COMMANDS.DONE} 完成添加目标\n` +
                `- 输入 ${SYSTEM_COMMANDS.CLEAR} 清除已添加的目标\n` +
                `- 输入 ${SYSTEM_COMMANDS.CANCEL} 取消操作`);
            break;
            
        case STATES.AWAITING_MESSAGES:
        case STATES.COLLECTING_MESSAGES:
        case STATES.COLLECTING_POST:
            // 收集转发内容，同时保存消息ID和聊天ID以便后续转发
            if (event && event.message) {
                // 检测是否为转发消息
                const isForwarded = !!event.message.fwdFrom;
                let forwardSource = null;
                let forwardDate = null;
                let originalPostId = null;
                
                if (isForwarded) {
                    // 提取转发消息的元数据
                    forwardSource = event.message.fwdFrom.fromId ? event.message.fwdFrom.fromId.toString() : null;
                    forwardDate = event.message.fwdFrom.date ? new Date(event.message.fwdFrom.date * 1000) : null;
                    
                    // 创建原始帖子ID (源ID + 日期)
                    if (forwardSource && forwardDate) {
                        originalPostId = `post_${forwardSource}_${Math.floor(forwardDate.getTime() / 1000)}`;
                        console.log(`[DEBUG] 检测到转发消息，原始帖子ID: ${originalPostId}`);
                    }
                    
                    console.log(`[DEBUG] 转发消息详情: 来源=${forwardSource}, 日期=${forwardDate}`);
                }
                
                // 检查是否有相册ID（媒体组）
                const originalGroupedId = event.message.groupedId;
                const hasMediaGroup = !!originalGroupedId;
                
                if (hasMediaGroup) {
                    console.log(`[DEBUG] 收到媒体组消息，groupedId: ${originalGroupedId}`);
                    console.log(`[DEBUG] 媒体消息详情: ID=${event.message.id}, 类型=${event.message.media ? event.message.media.className : '未知'}`);
                }
                
                // 检查消息是否包含媒体
                const hasMedia = !!event.message.media;
                const mediaType = hasMedia ? event.message.media.className : null;
                const isPhotoOrVideo = hasMedia && (
                    mediaType === 'MessageMediaPhoto' || 
                    (mediaType === 'MessageMediaDocument' && 
                     event.message.media.document && 
                     event.message.media.document.mimeType && 
                     (event.message.media.document.mimeType.startsWith('image/') || 
                      event.message.media.document.mimeType.startsWith('video/')))
                );
                
                // 帖子分组处理逻辑
                if (isForwarded && originalPostId) {
                    // 初始化用户的帖子组数据（如果不存在）
                    if (!pendingPostGroups[userId]) {
                        pendingPostGroups[userId] = {
                            currentPostId: null,
                            messages: [],
                            lastUpdate: Date.now(),
                            timer: null
                        };
                    }
                    
                    // 更新最后活动时间
                    pendingPostGroups[userId].lastUpdate = Date.now();
                    
                    // 如果是新帖子组或与当前帖子组不同
                    if (pendingPostGroups[userId].currentPostId !== originalPostId) {
                        // 如果之前有正在处理的帖子组，先处理完
                        if (pendingPostGroups[userId].currentPostId && pendingPostGroups[userId].messages.length > 0) {
                            await processPostGroup(client, userId);
                        }
                        
                        // 开始新的帖子组
                        pendingPostGroups[userId].currentPostId = originalPostId;
                        console.log(`[DEBUG] 开始新的转发帖子组: ${originalPostId}`);
                        
                        // 清除之前的计时器（如果有）
                        if (pendingPostGroups[userId].timer) {
                            clearTimeout(pendingPostGroups[userId].timer);
                        }
                        
                        // 向用户发送提示（仅在开始新帖子组时）
                        if (pendingPostGroups[userId].messages.length === 0) {
                            await sendMessage(client, userId, 
                                `📋 检测到转发帖子！\n\n` +
                                `系统将自动收集完整帖子内容，包括文本和媒体\n` +
                                `请继续转发此帖子的其他部分...\n` +
                                `系统将在${POST_GROUP_TIMEOUT/1000}秒后自动完成收集，或发送任何非转发消息立即完成`);
                        }
                        
                        userState.state = STATES.COLLECTING_POST;
                    }
                    
                    // 添加到当前帖子组
                    pendingPostGroups[userId].messages.push({
                        event: event,
                        message: message,
                        messageId: event.message.id,
                        chatId: (await event.message.getChat()).id.toString(),
                        groupedId: originalGroupedId,
                        originalPostId: originalPostId,
                        hasMedia: hasMedia,
                        mediaType: mediaType,
                        time: Date.now()
                    });
                    
                    console.log(`[DEBUG] 添加消息到帖子组 ${originalPostId}, 当前帖子组消息数: ${pendingPostGroups[userId].messages.length}`);
                    
                    // 设置自动完成计时器
                    if (pendingPostGroups[userId].timer) {
                        clearTimeout(pendingPostGroups[userId].timer);
                    }
                    
                    pendingPostGroups[userId].timer = setTimeout(async () => {
                        if (pendingPostGroups[userId] && pendingPostGroups[userId].messages.length > 0) {
                            console.log(`[DEBUG] 帖子组自动收集计时器触发, 帖子ID: ${pendingPostGroups[userId].currentPostId}`);
                            await processPostGroup(client, userId);
                        }
                    }, POST_GROUP_TIMEOUT);
                    
                    // 暂不进行常规消息处理，等待帖子组收集完成
                    return;
                } 
                else if (userState.state === STATES.COLLECTING_POST) {
                    // 如果正在收集帖子组，但收到了非转发消息，处理已收集的帖子组
                    if (pendingPostGroups[userId] && pendingPostGroups[userId].messages.length > 0) {
                        await processPostGroup(client, userId);
                    }
                }
                
                // 正常的消息收集逻辑（对于非相册情况或独立的相册消息）
                userState.data.messages.push(message);
                userState.data.messageIds.push(event.message.id);
                
                // 获取聊天ID
                const chat = await event.message.getChat();
                const chatId = chat.id.toString();
                userState.data.chatIds.push(chatId);
                
                // 保存groupedId，如果是原始媒体组消息，务必保留原始groupedId
                userState.data.groupedIds.push(originalGroupedId || null);
                
                // 添加更详细的媒体信息日志
                if (event.message.media) {
                    console.log(`[DEBUG] 媒体消息详情:`);
                    console.log(`- 媒体类型: ${event.message.media.className}`);
                    console.log(`- 消息ID: ${event.message.id}`);
                    console.log(`- 相册ID: ${originalGroupedId || '无'}`);
                    
                    // 尝试获取更多媒体信息
                    try {
                        if (event.message.media.photo) {
                            console.log(`- 照片ID: ${event.message.media.photo.id}`);
                            console.log(`- 照片尺寸: ${event.message.media.photo.sizes ? event.message.media.photo.sizes.length : '未知'}`);
                        }
                        if (event.message.media.document) {
                            console.log(`- 文档ID: ${event.message.media.document.id}`);
                            console.log(`- 文档名: ${event.message.media.document.attributes.find(a => a.fileName)?.fileName || '未知'}`);
                        }
                    } catch (err) {
                        console.log(`[ERROR] 获取媒体详情失败: ${err.message}`);
                    }
                }
                
                userState.state = STATES.COLLECTING_MESSAGES;
                console.log(`[DEBUG] 添加消息，当前状态: ${userState.state}, 消息总数: ${userState.data.messages.length}`);
                
                // 检查是否有媒体
                let mediaInfo = "";
                if (event.message.media) {
                    const mediaType = event.message.media.className;
                    mediaInfo = `[包含${mediaType}] `;
                    if (originalGroupedId) {
                        mediaInfo += `[相册ID: ${originalGroupedId}] `;
                    }
                }
                
                await sendMessage(client, userId, 
                    `✅ 已添加消息 #${userState.data.messages.length} ${mediaInfo}\n\n` +
                    `继续输入下一条消息，或:\n` +
                    `- 输入 ${SYSTEM_COMMANDS.DONE} 完成添加消息\n` +
                    `- 输入 ${SYSTEM_COMMANDS.CLEAR} 清除已添加的消息\n` +
                    `- 输入 ${SYSTEM_COMMANDS.CANCEL} 取消操作`);
            } else {
                // 如果无法获取消息对象，提示错误
                await sendMessage(client, userId, 
                    `❌ 无法获取完整消息信息，请重试。\n` +
                    `如果问题持续存在，请尝试重新发送消息。`);
            }
            break;
            
        case STATES.AWAITING_TIME:
            // 处理时间输入
            try {
                const scheduledTime = parseDateTime(message);
                userState.data.scheduledTime = scheduledTime;
                userState.state = STATES.AWAITING_CONFIRMATION;
                console.log(`[DEBUG] 设置时间: ${formatDate(scheduledTime)}, 当前状态: ${userState.state}`);
                
                // 创建预览消息
                let previewMsg = `⏰ 任务预览:\n\n`;
                previewMsg += `📅 发送时间: ${formatDate(scheduledTime)}\n\n`;
                previewMsg += `👥 发送目标: (${userState.data.targets.length}个)\n`;
                userState.data.targets.forEach((target, i) => {
                    previewMsg += `${i+1}. ${target}\n`;
                });
                
                previewMsg += `\n📝 发送内容: ${userState.data.messages.length}条消息\n`;
                
                // 显示消息预览
                userState.data.messages.forEach((msg, i) => {
                    let msgInfo = msg;
                    if (msg.length > 50) {
                        msgInfo = msg.substring(0, 50) + '...';
                    } else if (msg === '') {
                        msgInfo = '[媒体内容]';
                    }
                    previewMsg += `${i+1}. ${msgInfo}\n`;
                });
                
                previewMsg += `\n确认设置此定时任务吗? 请回复 "确认" 创建任务，或 ${SYSTEM_COMMANDS.CANCEL} 取消`;
                
                await sendMessage(client, userId, previewMsg);
            } catch (error) {
                await sendMessage(client, userId, 
                    `❌ 时间格式错误: ${error.message}\n` +
                    `请按格式 YYYY-MM-DD HH:MM 输入, 例如: 2025-07-01 14:30\n\n` +
                    `输入 ${SYSTEM_COMMANDS.CANCEL} 取消操作`);
            }
            break;
            
        case STATES.AWAITING_CONFIRMATION:
            // 处理确认回复
            if (message.toLowerCase() === '确认' || 
                message.toLowerCase() === 'confirm' ||
                message.toLowerCase() === 'yes' || 
                message.toLowerCase() === 'y') {
                
                console.log(`[DEBUG] 用户确认创建任务, 当前状态: ${userState.state}`);
                
                // 创建定时任务
                await createForwardJob(
                    client,
                    userId,
                    userState.data.scheduledTime,
                    userState.data.messageIds,
                    userState.data.chatIds,
                    userState.data.targets
                );
                
                // 重置状态
                userState.state = STATES.IDLE;
                userState.data = {
                    targets: [],
                    messages: [],
                    messageIds: [],
                    chatIds: [],
                    groupedIds: [],
                    postIds: [],
                    postUnits: []
                };
            } else {
                await sendMessage(client, userId, 
                    `❓ 请明确回复 "确认" 创建任务，或 ${SYSTEM_COMMANDS.CANCEL} 取消操作`);
            }
            break;
            
        case STATES.COLLECTING_TARGETS:
            userState.data.targets = [];
            await sendMessage(client, userId, 
                "🗑️ 已清除所有添加的转发目标\n" +
                "请重新输入转发目标");
            break;
            
        case STATES.COLLECTING_MESSAGES:
            userState.data.messages = [];
            userState.data.messageIds = [];
            userState.data.chatIds = [];
            userState.data.groupedIds = [];
            await sendMessage(client, userId, 
                "🗑️ 已清除所有添加的消息\n" +
                "请重新输入消息内容");
            break;
            
        default:
            // 空闲状态 - 提示用户使用命令
            console.log(`[DEBUG] 用户处于空闲状态, 提示使用命令`);
            await sendMessage(client, userId, "请使用命令进行操作。发送 /help 查看可用命令。");
            break;
    }
}

// 处理 /done 命令
async function handleDoneCommand(client, userId) {
    const userState = userStates[userId];
    console.log(`[DEBUG] handleDoneCommand - 用户: ${userId}, 状态: ${userState ? userState.state : 'undefined'}`);
    
    // 检查用户状态是否存在
    if (!userState) {
        console.log(`[ERROR] 用户状态不存在，为用户${userId}创建初始状态`);
        userStates[userId] = {
            state: STATES.IDLE,
            data: {
                targets: [],
                messages: [],
                messageIds: [],
                chatIds: [],
                groupedIds: [],
                postIds: [],
                postUnits: []
            }
        };
        await sendMessage(client, userId, "❓ 当前状态下无需使用此命令");
        return;
    }
    
    // 确保data对象存在
    if (!userState.data) {
        userState.data = {
            targets: [],
            messages: [],
            messageIds: [],
            chatIds: [],
            groupedIds: [],
            postIds: [],
            postUnits: []
        };
    }
    
    switch (userState.state) {
        case STATES.AWAITING_TARGETS:
        case STATES.COLLECTING_TARGETS:
            console.log(`[DEBUG] 目标收集状态下的/done命令处理, 目标数量: ${userState.data.targets ? userState.data.targets.length : 0}`);
            // 检查是否有目标
            if (!userState.data.targets || userState.data.targets.length === 0) {
                await sendMessage(client, userId, "❌ 请至少添加一个转发目标");
                return;
            }
            
            // 进入下一阶段 - 收集消息
            userState.state = STATES.AWAITING_MESSAGES;
            console.log(`[DEBUG] 状态转换: COLLECTING_TARGETS -> AWAITING_MESSAGES`);
            
            await sendMessage(client, userId, 
                `👥 已成功添加 ${userState.data.targets.length} 个转发目标\n\n` +
                `📝 现在请输入要发送的消息内容\n` +
                `每次输入一条消息，可以添加多条\n\n` +
                `输入 ${SYSTEM_COMMANDS.DONE} 完成添加消息\n` +
                `输入 ${SYSTEM_COMMANDS.CANCEL} 取消操作`);
            break;
            
        case STATES.AWAITING_MESSAGES:
        case STATES.COLLECTING_MESSAGES:
            console.log(`[DEBUG] 消息收集状态下的/done命令处理, 消息数量: ${userState.data.messages ? userState.data.messages.length : 0}`);
            // 检查是否有消息
            if (!userState.data.messages || userState.data.messages.length === 0) {
                await sendMessage(client, userId, "❌ 请至少添加一条消息");
                return;
            }
            
            // 进入下一阶段 - 设置时间
            userState.state = STATES.AWAITING_TIME;
            console.log(`[DEBUG] 状态转换: COLLECTING_MESSAGES -> AWAITING_TIME`);
            
            await sendMessage(client, userId, 
                `📝 已成功添加 ${userState.data.messages.length} 条消息\n\n` +
                `⏰ 请设置发送时间，格式: YYYY-MM-DD HH:MM\n` +
                `例如: 2025-07-01 14:30 表示2025年7月1日14点30分\n\n` +
                `输入 ${SYSTEM_COMMANDS.CANCEL} 取消操作`);
            break;
            
        default:
            console.log(`[DEBUG] 状态 ${userState.state} 下无法处理/done命令`);
            await sendMessage(client, userId, "❓ 当前状态下无需使用此命令");
            break;
    }
}

// 处理 /clear 命令
async function handleClearCommand(client, userId) {
    const userState = userStates[userId];
    console.log(`[DEBUG] handleClearCommand - 用户: ${userId}, 状态: ${userState ? userState.state : 'undefined'}`);
    
    if (!userState) {
        userStates[userId] = {
            state: STATES.IDLE,
            data: {
                targets: [],
                messages: [],
                messageIds: [],
                chatIds: [],
                groupedIds: [],
                postIds: [],
                postUnits: []
            }
        };
        await sendMessage(client, userId, "❓ 当前状态下无需使用此命令");
        return;
    }
    
    switch (userState.state) {
        case STATES.COLLECTING_TARGETS:
            userState.data.targets = [];
            await sendMessage(client, userId, 
                "🗑️ 已清除所有添加的转发目标\n" +
                "请重新输入转发目标");
            break;
            
        case STATES.COLLECTING_MESSAGES:
            userState.data.messages = [];
            userState.data.messageIds = [];
            userState.data.chatIds = [];
            userState.data.groupedIds = [];
            await sendMessage(client, userId, 
                "🗑️ 已清除所有添加的消息\n" +
                "请重新输入消息内容");
            break;
            
        default:
            await sendMessage(client, userId, "❓ 当前状态下无需使用此命令");
            break;
    }
}

// 新增函数：确保媒体已下载
async function ensureMediaDownloaded(client, message) {
    if (!message.media) return;
    
    try {
        console.log(`[DEBUG] 确保媒体已下载: 消息ID ${message.id}`);
        
        // 根据媒体类型处理
        if (message.media.photo) {
            // 下载照片
            const photoInfo = message.media.photo;
            console.log(`[DEBUG] 准备下载照片: ID ${photoInfo.id}`);
            
            // 获取照片最佳尺寸
            const sizeIndex = photoInfo.sizes ? photoInfo.sizes.length - 1 : 0;
            
            // 下载照片
            await client.downloadMedia(message.media, {
                progressCallback: (progress) => {
                    if (progress % 25 === 0) { // 每25%记录一次
                        console.log(`[DEBUG] 照片下载进度: ${progress}%`);
                    }
                }
            });
            console.log(`[DEBUG] 照片下载完成: ${photoInfo.id}`);
        } 
        else if (message.media.document) {
            // 下载文档（可能是视频、GIF等）
            const docInfo = message.media.document;
            console.log(`[DEBUG] 准备下载文档: ID ${docInfo.id}`);
            
            // 下载文档
            await client.downloadMedia(message.media, {
                progressCallback: (progress) => {
                    if (progress % 25 === 0) { // 每25%记录一次
                        console.log(`[DEBUG] 文档下载进度: ${progress}%`);
                    }
                }
            });
            console.log(`[DEBUG] 文档下载完成: ${docInfo.id}`);
        }
        
        return true;
    } catch (error) {
        console.error(`[ERROR] 媒体下载失败: ${error.message}`);
        return false;
    }
}

// 修改createForwardJob函数，在转发前确保媒体已下载
async function createForwardJob(client, userId, scheduledTime, messageIds, chatIds, targets) {
    try {
        // 生成任务ID
        const jobId = `job_${Date.now()}`;
        
        // 设置执行时间的Unix时间戳
        const timestamp = Math.floor(scheduledTime.getTime() / 1000);
        
        // 获取groupedIds
        const groupedIds = userStates[userId].data.groupedIds || [];
        
        // 获取帖子ID（如果有）
        const postIds = userStates[userId].data.postIds || [];
        
        // 获取帖子单元（如果有）
        const postUnits = userStates[userId].data.postUnits || [];
        
        // 创建一个一次性的定时任务
        const job = schedule.scheduleJob(scheduledTime, async () => {
            try {
                console.log(`执行定时任务 ${jobId}，向 ${targets.length} 个目标转发消息`);
                
                // 记录发送结果
                const results = {
                    success: [],
                    failed: []
                };
                
                // 详细记录要转发的消息
                console.log(`[DEBUG] 准备转发消息，数量: ${messageIds.length}`);
                console.log(`[DEBUG] groupedIds: ${JSON.stringify(groupedIds)}`);
                
                // 收集所有需要转发的消息对象
                const messageObjects = [];
                
                console.log(`[DEBUG] 准备收集消息对象，总数: ${messageIds.length}`);
                
                // 先获取所有消息对象
                for (let i = 0; i < messageIds.length; i++) {
                    try {
                        // 获取完整消息对象
                        const peer = await client.getInputEntity(chatIds[i]);
                        const messages = await client.getMessages(peer, {ids: [messageIds[i]]});
                        
                        if (messages && messages.length > 0) {
                            const msg = messages[0];
                            console.log(`[DEBUG] 成功获取消息: ID ${msg.id}, 类型: ${msg.media ? msg.media.className : '文本'}`);
                            if (msg.groupedId) {
                                console.log(`[DEBUG] 原始相册ID: ${msg.groupedId}`);
                            }
                            
                            // 如果是媒体消息，确保已下载
                            if (msg.media) {
                                await ensureMediaDownloaded(client, msg);
                            }
                            
                            messageObjects.push({
                                object: msg,
                                messageId: messageIds[i],
                                chatId: chatIds[i],
                                groupedId: groupedIds[i] || null
                            });
                        } else {
                            console.log(`[WARNING] 无法获取消息: ID ${messageIds[i]}`);
                        }
                    } catch (err) {
                        console.error(`[ERROR] 获取消息 ${messageIds[i]} 失败: ${err.message}`);
                    }
                    
                    // 添加短暂延迟避免请求过快
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
                
                console.log(`[DEBUG] 成功收集 ${messageObjects.length}/${messageIds.length} 条消息`);
                
                // 准备要转发的消息
                const messages = [];
                
                // 组织消息，并按照groupedId和postId分组
                const albumMessages = {};
                const albumSequence = []; // 记录相册ID的顺序
                const postMessages = {}; // 按帖子ID分组的消息
                const postSequence = []; // 记录帖子ID的顺序
                
                // 先处理完整帖子单元
                if (postUnits && postUnits.length > 0) {
                    console.log(`[DEBUG] 处理 ${postUnits.length} 个完整帖子单元`);
                    
                    for (const postUnit of postUnits) {
                        const postId = postUnit.postId;
                        if (!postMessages[postId]) {
                            postMessages[postId] = {
                                messages: [],
                                albums: {}
                            };
                            postSequence.push(postId);
                        }
                        
                        // 处理帖子中的每条消息
                        for (const msg of postUnit.messages) {
                            // 检查是否是相册的一部分
                            if (msg.groupedId) {
                                if (!postMessages[postId].albums[msg.groupedId]) {
                                    postMessages[postId].albums[msg.groupedId] = {
                                        chatId: msg.chatId,
                                        messageIds: []
                                    };
                                }
                                postMessages[postId].albums[msg.groupedId].messageIds.push(msg.messageId);
                            } else {
                                postMessages[postId].messages.push({
                                    messageId: msg.messageId,
                                    chatId: msg.chatId
                                });
                            }
                        }
                    }
                }
                
                // 处理其他单独消息
                for (let i = 0; i < messageIds.length; i++) {
                    const postId = postIds[i] || null;
                    
                    // 如果这条消息是帖子的一部分，且我们已经处理了该帖子单元，则跳过
                    if (postId && postMessages[postId]) {
                        continue;
                    }
                    
                    const msg = {
                        messageId: messageIds[i],
                        chatId: chatIds[i],
                        groupedId: groupedIds[i] || null
                    };
                    
                    // 如果是帖子的一部分，但帖子单元未处理（可能是旧数据），按帖子分组
                    if (postId) {
                        if (!postMessages[postId]) {
                            postMessages[postId] = {
                                messages: [],
                                albums: {}
                            };
                            postSequence.push(postId);
                        }
                        
                        if (msg.groupedId) {
                            if (!postMessages[postId].albums[msg.groupedId]) {
                                postMessages[postId].albums[msg.groupedId] = {
                                    chatId: msg.chatId,
                                    messageIds: []
                                };
                            }
                            postMessages[postId].albums[msg.groupedId].messageIds.push(msg.messageId);
                        } else {
                            postMessages[postId].messages.push({
                                messageId: msg.messageId,
                                chatId: msg.chatId
                            });
                        }
                    }
                    // 处理常规相册消息
                    else if (msg.groupedId) {
                        if (!albumMessages[msg.groupedId]) {
                            albumMessages[msg.groupedId] = {
                                chatId: msg.chatId,
                                messageIds: [],
                                objects: []
                            };
                            albumSequence.push(msg.groupedId);
                            console.log(`[DEBUG] 创建新相册组: ${msg.groupedId}`);
                        }
                        albumMessages[msg.groupedId].messageIds.push(msg.messageId);
                        console.log(`[DEBUG] 添加消息到相册组 ${msg.groupedId}: 消息ID=${msg.messageId}`);
                    } 
                    // 处理独立消息
                    else {
                        messages.push(msg);
                        console.log(`[DEBUG] 添加独立消息: ID=${msg.messageId}`);
                    }
                }
                
                // 对每个目标转发消息
                for (const target of targets) {
                    try {
                        let forwardSuccess = true;
                        
                        console.log(`[DEBUG] 开始向目标 ${target} 转发消息`);
                        
                        // 1. 先转发完整帖子
                        if (postSequence.length > 0) {
                            console.log(`[DEBUG] 转发 ${postSequence.length} 个完整帖子`);
                            
                            for (const postId of postSequence) {
                                const post = postMessages[postId];
                                console.log(`[DEBUG] 转发帖子 ${postId}, 包含 ${post.messages.length} 条消息和 ${Object.keys(post.albums).length} 个相册`);
                                
                                // 先转发帖子中的相册
                                for (const albumId in post.albums) {
                                    const album = post.albums[albumId];
                                    console.log(`[DEBUG] 转发帖子内相册 ${albumId}, 共 ${album.messageIds.length} 张媒体`);
                                    
                                    try {
                                        await client.forwardMessages(target, {
                                            messages: album.messageIds,
                                            fromPeer: album.chatId,
                                            withMyScore: true,
                                            dropAuthor: true, // 修改为true，去除"转发自xxx"
                                            dropMediaCaptions: false
                                        });
                                        console.log(`[DEBUG] 帖子内相册 ${albumId} 转发成功`);
                                    } catch (err) {
                                        console.error(`[ERROR] 帖子内相册 ${albumId} 转发失败: ${err.message}`);
                                        // 尝试逐个转发
                                        for (const msgId of album.messageIds) {
                                            try {
                                                await client.forwardMessages(target, {
                                                    messages: [msgId],
                                                    fromPeer: album.chatId,
                                                    withMyScore: true,
                                                    dropAuthor: true, // 修改为true，去除"转发自xxx"
                                                    dropMediaCaptions: false
                                                });
                                            } catch (singleErr) {
                                                console.error(`[ERROR] 单条消息 ${msgId} 转发失败: ${singleErr.message}`);
                                                forwardSuccess = false;
                                            }
                                            await new Promise(resolve => setTimeout(resolve, 1000));
                                        }
                                    }
                                    await new Promise(resolve => setTimeout(resolve, 2000));
                                }
                                
                                // 再转发帖子中的独立消息
                                for (const msg of post.messages) {
                                    try {
                                        await client.forwardMessages(target, {
                                            messages: [msg.messageId],
                                            fromPeer: msg.chatId,
                                            withMyScore: true,
                                            dropAuthor: true, // 修改为true，去除"转发自xxx"
                                            dropMediaCaptions: false
                                        });
                                        console.log(`[DEBUG] 帖子内消息 ${msg.messageId} 转发成功`);
                                    } catch (err) {
                                        console.error(`[ERROR] 帖子内消息 ${msg.messageId} 转发失败: ${err.message}`);
                                        forwardSuccess = false;
                                    }
                                    await new Promise(resolve => setTimeout(resolve, 1000));
                                }
                                
                                // 帖子转发完成，额外等待
                                await new Promise(resolve => setTimeout(resolve, 2000));
                            }
                        }
                        
                        // 2. 转发普通相册消息（按照添加顺序）
                        for (const albumId in albumMessages) {
                            const album = albumMessages[albumId];
                            console.log(`[DEBUG] 转发相册 ${albumId}, 共 ${album.messageIds.length} 张媒体`);
                            
                            try {
                                await client.forwardMessages(target, {
                                    messages: album.messageIds,
                                    fromPeer: album.chatId,
                                    withMyScore: true,
                                    dropAuthor: true, // 修改为true，去除"转发自xxx"
                                    dropMediaCaptions: false
                                });
                                console.log(`[DEBUG] 相册 ${albumId} 转发成功`);
                            } catch (err) {
                                console.error(`[ERROR] 相册 ${albumId} 转发失败: ${err.message}`);
                                // 尝试逐个转发
                                for (const msgId of album.messageIds) {
                                    try {
                                        await client.forwardMessages(target, {
                                            messages: [msgId],
                                            fromPeer: album.chatId,
                                            withMyScore: true,
                                            dropAuthor: true, // 修改为true，去除"转发自xxx"
                                            dropMediaCaptions: false
                                        });
                                    } catch (singleErr) {
                                        console.error(`[ERROR] 单条消息 ${msgId} 转发失败: ${singleErr.message}`);
                                        forwardSuccess = false;
                                    }
                                    await new Promise(resolve => setTimeout(resolve, 1000));
                                }
                            }
                            await new Promise(resolve => setTimeout(resolve, 2000));
                        }
                        
                        // 3. 转发其他非相册独立消息
                        for (const msg of messages) {
                            try {
                            await client.forwardMessages(target, {
                                messages: [msg.messageId],
                                fromPeer: msg.chatId,
                                withMyScore: true,
                                    dropAuthor: true, // 修改为true，去除"转发自xxx"
                                dropMediaCaptions: false
                            });
                            
                                console.log(`[DEBUG] 单条消息 ${msg.messageId} 转发成功`);
                                await new Promise(resolve => setTimeout(resolve, 1500));
                            } catch (err) {
                                console.error(`[ERROR] 单条消息 ${msg.messageId} 转发失败: ${err.message}`);
                                
                                // 尝试复制媒体并发送
                                try {
                                    if (msg.object && msg.object.media) {
                                        console.log(`[DEBUG] 尝试复制媒体并发送: ${msg.messageId}`);
                                        await client.sendMessage(target, {
                                            message: msg.object.message || '',
                                            media: msg.object.media
                                        });
                                        console.log(`[DEBUG] 复制媒体发送成功: ${msg.messageId}`);
                                    } else if (msg.object) {
                                        await client.sendMessage(target, {
                                            message: msg.object.message || ''
                                        });
                                    }
                                } catch (copyErr) {
                                    console.error(`[ERROR] 复制发送失败: ${copyErr.message}`);
                                    forwardSuccess = false;
                                }
                            }
                        }
                        
                        if (forwardSuccess) {
                        results.success.push(target);
                        } else {
                            results.failed.push({ target, error: "部分消息转发失败" });
                        }
                    } catch (error) {
                        console.error(`向目标 ${target} 转发消息失败:`, error.message);
                        results.failed.push({ target, error: error.message });
                    }
                }
                
                console.log(`定时任务 ${jobId} 执行完成，成功: ${results.success.length}，失败: ${results.failed.length}`);
                
                // 构建执行结果报告
                let resultMsg = `✅ 定时任务 ${jobId} 执行完成\n\n`;
                resultMsg += `📊 发送统计:\n`;
                resultMsg += `- 目标总数: ${targets.length}\n`;
                resultMsg += `- 成功发送: ${results.success.length}\n`;
                resultMsg += `- 失败数量: ${results.failed.length}\n`;
                
                if (results.success.length > 0) {
                    resultMsg += `\n✅ 成功发送到:\n`;
                    results.success.forEach((target, index) => {
                        resultMsg += `- ${target}\n`;
                    });
                }
                
                if (results.failed.length > 0) {
                    resultMsg += `\n❌ 发送失败的目标:\n`;
                    results.failed.forEach(({ target, error }) => {
                        resultMsg += `- ${target}: ${error}\n`;
                    });
                }
                
                // 通知用户任务执行结果
                await sendMessage(client, userId, resultMsg);
                
                // 从存储中移除任务（一次性任务）
                delete scheduledJobs[jobId];
                
            } catch (error) {
                console.error(`定时任务 ${jobId} 执行失败:`, error);
                
                // 通知用户任务执行失败
                await sendMessage(client, userId, `❌ 定时任务 ${jobId} 执行失败: ${error.message}`);
                
                // 从存储中移除任务（一次性任务）
                delete scheduledJobs[jobId];
            }
        });
        
        // 存储任务信息，添加帖子数据
        scheduledJobs[jobId] = {
            id: jobId,
            userId: userId,
            scheduledTime: scheduledTime,
            timestamp: timestamp,
            targets: targets,
            messageCount: messageIds.length,
            messageIds: messageIds,
            chatIds: chatIds,
            groupedIds: groupedIds,
            postIds: postIds,
            postUnits: postUnits,
            isForward: true,
            job: job
        };
        
        // 发送成功消息
        await sendMessage(client, userId, 
            `✅ 定时任务创建成功！\n\n` +
            `🆔 任务ID: ${jobId}\n` +
            `⏰ 发送时间: ${formatDate(scheduledTime)}\n` +
            `👥 目标数量: ${targets.length}\n` +
            `📝 消息数量: ${messageIds.length}条\n\n` +
            `发送 /list 查看所有任务\n` +
            `发送 /cancel ${jobId} 取消此任务`);
            
        return jobId;
    } catch (error) {
        console.error('创建定时任务失败:', error);
        await sendMessage(client, userId, `❌ 创建定时任务失败: ${error.message}`);
        return null;
    }
}

// 修改列出任务函数，支持新的任务格式
async function listJobs(client, userId) {
    // 获取用户的任务
    const userJobs = Object.values(scheduledJobs).filter(job => job.userId === userId);
    
    if (userJobs.length === 0) {
        await sendMessage(client, userId, "📭 您当前没有设置任何定时任务");
        return;
    }
    
    let message = "📋 您的定时任务列表：\n\n";
    
    userJobs.forEach((job, index) => {
        message += `${index + 1}. 🆔 ${job.id}\n`;
        
        if (job.scheduledTime) {
            message += `   ⏰ 发送时间: ${formatDate(job.scheduledTime)}\n`;
        } else if (job.cronTime) {
            message += `   ⏰ 定时规则: ${job.cronTime}\n`;
            const nextRun = job.job.nextInvocation() ? job.job.nextInvocation().toString() : '未知';
            message += `   ⏱️ 下次执行: ${nextRun}\n`;
        }
        
        if (job.targets) {
            message += `   👥 目标数量: ${job.targets.length}\n`;
            message += `   📝 消息数量: ${job.messageCount}\n`;
        } else {
            message += `   🎯 目标: ${job.targetChat}\n`;
            const msgPreview = job.messageContent ? 
                (job.messageContent.length > 30 ? job.messageContent.substring(0, 30) + '...' : job.messageContent) : 
                '(无预览)';
            message += `   📝 消息: ${msgPreview}\n`;
        }
        
        message += `   ❌ 取消: /cancel ${job.id}\n\n`;
    });
    
    await sendMessage(client, userId, message);
}

// 解析日期时间
function parseDateTime(dateTimeStr) {
    console.log(`[DEBUG] 解析日期时间: "${dateTimeStr}"`);
    
    // 尝试不同的日期格式
    let date = null;
    
    // 1. 标准格式: YYYY-MM-DD HH:MM
    const pattern1 = /^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2})$/;
    const matches1 = dateTimeStr.match(pattern1);
    
    if (matches1) {
        console.log(`[DEBUG] 匹配标准格式 YYYY-MM-DD HH:MM`);
        const year = parseInt(matches1[1]);
        const month = parseInt(matches1[2]) - 1; // 月份从0开始计数
        const day = parseInt(matches1[3]);
        const hour = parseInt(matches1[4]);
        const minute = parseInt(matches1[5]);
        
        date = new Date(year, month, day, hour, minute);
    } else {
        // 2. 替代格式: YYYY-M-D H:M 或 YYYY/MM/DD HH:MM
        const pattern2 = /^(\d{4})[-\/](\d{1,2})[-\/](\d{1,2}) (\d{1,2}):(\d{1,2})$/;
        const matches2 = dateTimeStr.match(pattern2);
        
        if (matches2) {
            console.log(`[DEBUG] 匹配替代格式 YYYY-M-D H:M 或 YYYY/MM/DD HH:MM`);
            const year = parseInt(matches2[1]);
            const month = parseInt(matches2[2]) - 1;
            const day = parseInt(matches2[3]);
            const hour = parseInt(matches2[4]);
            const minute = parseInt(matches2[5]);
            
            date = new Date(year, month, day, hour, minute);
        } else {
            // 3. 仅日期格式: YYYY-MM-DD (假设时间为00:00)
            const pattern3 = /^(\d{4})[-\/](\d{1,2})[-\/](\d{1,2})$/;
            const matches3 = dateTimeStr.match(pattern3);
            
            if (matches3) {
                console.log(`[DEBUG] 匹配日期格式 YYYY-MM-DD`);
                const year = parseInt(matches3[1]);
                const month = parseInt(matches3[2]) - 1;
                const day = parseInt(matches3[3]);
                
                date = new Date(year, month, day, 0, 0);
            } else {
                console.log(`[ERROR] 无法匹配任何日期格式`);
                throw new Error('时间格式无效，请使用 YYYY-MM-DD HH:MM 格式，例如: 2025-07-01 14:30');
            }
        }
    }
    
    // 验证日期是否有效
    if (!date || isNaN(date.getTime())) {
        console.log(`[ERROR] 无效的日期时间: ${date}`);
        throw new Error('无效的日期时间，请检查年月日是否正确');
    }
    
    // 检查日期是否过去
    const now = new Date();
    if (date <= now) {
        console.log(`[ERROR] 过去的时间: ${formatDate(date)}, 当前时间: ${formatDate(now)}`);
        throw new Error(`不能设置过去的时间，请设置未来的时间 (当前时间: ${formatDate(now)})`);
    }
    
    console.log(`[DEBUG] 解析后的时间: ${formatDate(date)}`);
    return date;
}

// 格式化日期
function formatDate(date) {
    const pad = (num) => num.toString().padStart(2, '0');
    
    const year = date.getFullYear();
    const month = pad(date.getMonth() + 1);
    const day = pad(date.getDate());
    const hour = pad(date.getHours());
    const minute = pad(date.getMinutes());
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
}

// 取消任务
async function cancelJob(client, userId, jobId) {
    // 检查任务是否存在
    if (!scheduledJobs[jobId]) {
        await sendMessage(client, userId, `❌ 任务不存在: ${jobId}`);
        return false;
    }
    
    // 检查是否是用户自己的任务
    if (scheduledJobs[jobId].userId !== userId) {
        await sendMessage(client, userId, `⛔ 您无权取消此任务`);
        return false;
    }
    
    try {
        // 取消任务
        scheduledJobs[jobId].job.cancel();
        
        // 从存储中删除
        delete scheduledJobs[jobId];
        
        await sendMessage(client, userId, `✅ 任务 ${jobId} 已成功取消`);
        return true;
    } catch (error) {
        console.error(`取消任务 ${jobId} 失败:`, error);
        await sendMessage(client, userId, `❌ 取消任务失败: ${error.message}`);
        return false;
    }
}

// 发送消息工具函数
async function sendMessage(client, userId, text) {
    try {
        console.log(`[DEBUG] 尝试向用户 ${userId} 发送消息: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`);
        await client.sendMessage(userId, { message: text });
        console.log(`[DEBUG] 成功向用户 ${userId} 发送消息`);
    } catch (error) {
        console.error(`向用户 ${userId} 发送消息失败:`, error.message);
        
        // 如果是连接错误，尝试重连后重试
        if (error.message.includes('Not connected') && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
            console.log('发送消息失败，尝试重连...');
            await handleReconnect();
            try {
                await client.sendMessage(userId, { message: text });
                console.log('重连后消息发送成功');
            } catch (retryError) {
                console.error('重连后发送消息仍然失败:', retryError.message);
                
                // 尝试另一种发送方式 - 使用Telegram实体
                try {
                    const entity = await client.getInputEntity(userId.toString());
                    await client.sendMessage(entity, { message: text });
                    console.log('使用实体方式发送消息成功');
                } catch (entityError) {
                    console.error('所有发送方式均失败:', entityError.message);
                }
            }
        } else {
            // 尝试另一种发送方式 - 使用Telegram实体
            try {
                const entity = await client.getInputEntity(userId.toString());
                await client.sendMessage(entity, { message: text });
                console.log('使用实体方式发送消息成功');
            } catch (entityError) {
                console.error('所有发送方式均失败:', entityError.message);
            }
        }
    }
}

// 添加处理媒体收集的函数
async function processMediaCollection(client, userId) {
    if (!pendingAlbums[userId] || !pendingAlbums[userId].messages || pendingAlbums[userId].messages.length === 0) {
        return;
    }
    
    // 取消收集状态
    pendingAlbums[userId].collecting = false;
    
    // 清除计时器
    if (pendingAlbums[userId].timer) {
        clearTimeout(pendingAlbums[userId].timer);
        pendingAlbums[userId].timer = null;
    }
    
    // 获取收集到的所有媒体消息
    const mediaMessages = pendingAlbums[userId].messages;
    const count = mediaMessages.length;
    
    console.log(`[DEBUG] 处理收集到的媒体消息，数量: ${count}`);
    
    // 确保用户状态正确
    if (!userStates[userId] || !userStates[userId].data) {
        console.log(`[ERROR] 用户状态无效，无法处理媒体收集`);
        pendingAlbums[userId].messages = [];
        return;
    }
    
    // 设置虚拟相册ID (使用时间戳作为ID)
    const virtualAlbumId = `album_${Date.now()}`;
    console.log(`[DEBUG] 创建虚拟相册 ID: ${virtualAlbumId}, 包含 ${count} 张媒体`);
    
    // 添加所有媒体到用户状态
    for (const item of mediaMessages) {
        const event = item.event;
        const message = item.message;
        
        userStates[userId].data.messages.push(message);
        userStates[userId].data.messageIds.push(event.message.id);
        
        // 获取聊天ID
        const chat = await event.message.getChat();
        const chatId = chat.id.toString();
        userStates[userId].data.chatIds.push(chatId);
        
        // 为所有媒体使用同一个虚拟相册ID
        userStates[userId].data.groupedIds.push(virtualAlbumId);
    }
    
    // 清空临时存储
    pendingAlbums[userId].messages = [];
    
    // 通知用户
    await sendMessage(client, userId, 
        `✅ 已将 ${count} 张媒体作为一个相册添加！\n\n` +
        `继续输入下一条消息，或:\n` +
        `- 输入 ${SYSTEM_COMMANDS.DONE} 完成添加消息\n` +
        `- 输入 ${SYSTEM_COMMANDS.CLEAR} 清除已添加的消息\n` +
        `- 输入 ${SYSTEM_COMMANDS.CANCEL} 取消操作`);
}

// 添加处理帖子组的函数
async function processPostGroup(client, userId) {
    if (!pendingPostGroups[userId] || !pendingPostGroups[userId].messages || pendingPostGroups[userId].messages.length === 0) {
        return;
    }
    
    // 清除计时器
    if (pendingPostGroups[userId].timer) {
        clearTimeout(pendingPostGroups[userId].timer);
        pendingPostGroups[userId].timer = null;
    }
    
    // 获取收集到的所有帖子组消息
    const postMessages = pendingPostGroups[userId].messages;
    const postId = pendingPostGroups[userId].currentPostId;
    const count = postMessages.length;
    
    console.log(`[DEBUG] 处理收集到的帖子组 ${postId}，消息数量: ${count}`);
    
    // 确保用户状态正确
    if (!userStates[userId] || !userStates[userId].data) {
        console.log(`[ERROR] 用户状态无效，无法处理帖子组`);
        pendingPostGroups[userId].messages = [];
        pendingPostGroups[userId].currentPostId = null;
        return;
    }
    
    // 将整个帖子作为一个单元保存
    const postUnit = {
        postId: postId,
        messages: []
    };
    
    // 添加所有消息到用户状态和帖子单元
    for (const item of postMessages) {
        userStates[userId].data.messages.push(item.message);
        userStates[userId].data.messageIds.push(item.messageId);
        userStates[userId].data.chatIds.push(item.chatId);
        userStates[userId].data.groupedIds.push(item.groupedId || null);
        
        // 额外保存帖子ID，用于后续转发时识别同一帖子的消息
        if (!userStates[userId].data.postIds) {
            userStates[userId].data.postIds = [];
        }
        userStates[userId].data.postIds.push(postId);
        
        // 添加到帖子单元
        postUnit.messages.push({
            messageId: item.messageId,
            chatId: item.chatId,
            groupedId: item.groupedId,
            hasMedia: item.hasMedia,
            mediaType: item.mediaType
        });
    }
    
    // 保存帖子单元到用户状态
    if (!userStates[userId].data.postUnits) {
        userStates[userId].data.postUnits = [];
    }
    userStates[userId].data.postUnits.push(postUnit);
    
    // 清空临时存储
    pendingPostGroups[userId].messages = [];
    pendingPostGroups[userId].currentPostId = null;
    
    // 通知用户
    await sendMessage(client, userId, 
        `✅ 已将整个转发帖子(${count}条消息)添加为一个单元!\n\n` +
        `继续添加其他内容，或:\n` +
        `- 输入 ${SYSTEM_COMMANDS.DONE} 完成添加消息\n` +
        `- 输入 ${SYSTEM_COMMANDS.CLEAR} 清除已添加的消息\n` +
        `- 输入 ${SYSTEM_COMMANDS.CANCEL} 取消操作`);
        
    // 更新用户状态
    userStates[userId].state = STATES.COLLECTING_MESSAGES;
}

// 捕获错误
main().catch(err => {
    console.error('出现错误:', err);
});

// 优雅地退出
process.on('SIGINT', () => {
    console.log('正在关闭...');
    
    // 取消所有定时任务
    Object.values(scheduledJobs).forEach(job => {
        try {
            job.job.cancel();
            console.log(`已取消任务: ${job.id}`);
        } catch (error) {
            console.error(`取消任务 ${job.id} 时出错:`, error);
        }
    });
    
    // 断开Telegram连接
    if (client) {
        client.disconnect()
            .then(() => console.log('已断开Telegram连接'))
            .catch(err => console.error('断开连接时出错:', err))
            .finally(() => process.exit(0));
    } else {
        process.exit(0);
    }
});